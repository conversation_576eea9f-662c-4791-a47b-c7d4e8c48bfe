#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有脚本的时间设置
显示当前的日期配置和建议的最新设置
"""

import os
import re
from datetime import datetime, timedelta
from pathlib import Path

class TimeSettingChecker:
    """时间设置检查器"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.scripts = [
            '全量股票日线.py',
            '全量股票日线123.py', 
            '指数全量.py',
            'tushare_index_data.py',
            '财务数据分开表格全量更新.py',
            '分红数据分开表格全量更新.py'
        ]
    
    def get_latest_trading_date(self):
        """获取最新的交易日期"""
        now = datetime.now()
        
        # 如果是周末，调整到最近的交易日
        if now.weekday() == 5:  # 周六
            now = now - timedelta(days=1)
        elif now.weekday() == 6:  # 周日
            now = now - timedelta(days=2)
        
        # 如果是当天且时间早于15:30，使用前一个交易日
        if now.hour < 15 or (now.hour == 15 and now.minute < 30):
            now = now - timedelta(days=1)
            # 再次检查是否是周末
            if now.weekday() == 5:  # 周六
                now = now - timedelta(days=1)
            elif now.weekday() == 6:  # 周日
                now = now - timedelta(days=2)
        
        return now.strftime('%Y%m%d')
    
    def check_script_time_settings(self, script_file):
        """检查单个脚本的时间设置"""
        script_path = self.current_dir / script_file
        
        if not script_path.exists():
            return {
                'file': script_file,
                'exists': False,
                'error': '文件不存在'
            }
        
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                'file': script_file,
                'exists': True,
                'has_date_params': False,
                'hardcoded_dates': [],
                'date_functions': [],
                'recommendations': []
            }
            
            # 检查是否有日期参数
            if 'start_date' in content and 'end_date' in content:
                result['has_date_params'] = True
            
            # 查找硬编码的日期
            date_patterns = [
                r'\d{8}',  # YYYYMMDD格式
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD格式
                r"['\"]20\d{6}['\"]",  # 引号包围的日期
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if len(match) == 8 and match.isdigit():
                        # 验证是否是有效日期
                        try:
                            datetime.strptime(match, '%Y%m%d')
                            result['hardcoded_dates'].append(match)
                        except ValueError:
                            pass
            
            # 查找日期相关的函数调用
            date_function_patterns = [
                r'fetch_all_stocks_daily_data\([^)]*\)',
                r'pro\.daily\([^)]*\)',
                r'pro\.index_daily\([^)]*\)',
                r'datetime\.now\(\)',
                r'datetime\.today\(\)'
            ]
            
            for pattern in date_function_patterns:
                matches = re.findall(pattern, content)
                result['date_functions'].extend(matches)
            
            # 生成建议
            if result['hardcoded_dates']:
                result['recommendations'].append('发现硬编码日期，建议改为动态获取')
            
            if not result['has_date_params'] and script_file in ['全量股票日线.py']:
                result['recommendations'].append('建议添加日期参数支持')
            
            if 'datetime.now()' not in content and script_file not in ['指数全量.py', 'tushare_index_data.py']:
                result['recommendations'].append('建议添加自动日期获取功能')
            
            return result
            
        except Exception as e:
            return {
                'file': script_file,
                'exists': True,
                'error': f'读取文件失败: {e}'
            }
    
    def generate_report(self):
        """生成时间设置报告"""
        print("="*60)
        print("脚本时间设置检查报告")
        print("="*60)
        
        latest_date = self.get_latest_trading_date()
        print(f"建议的最新交易日期: {latest_date}")
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        for script_file in self.scripts:
            print(f"📄 {script_file}")
            print("-" * 40)
            
            result = self.check_script_time_settings(script_file)
            
            if not result['exists']:
                print(f"❌ {result['error']}")
                print()
                continue
            
            if 'error' in result:
                print(f"❌ {result['error']}")
                print()
                continue
            
            # 显示检查结果
            if result['has_date_params']:
                print("✅ 支持日期参数")
            else:
                print("⚠️  不支持日期参数")
            
            if result['hardcoded_dates']:
                print(f"⚠️  发现硬编码日期: {', '.join(set(result['hardcoded_dates']))}")
            else:
                print("✅ 未发现硬编码日期")
            
            if result['date_functions']:
                print("📋 日期相关函数:")
                unique_functions = list(set(result['date_functions']))
                for func in unique_functions[:3]:  # 只显示前3个
                    print(f"   - {func}")
                if len(unique_functions) > 3:
                    print(f"   ... 还有 {len(unique_functions) - 3} 个")
            
            if result['recommendations']:
                print("💡 建议:")
                for rec in result['recommendations']:
                    print(f"   - {rec}")
            
            print()
        
        print("="*60)
        print("总结")
        print("="*60)
        print("🔧 使用 '启动所有脚本.py' 可以自动设置最新日期并运行所有脚本")
        print("📅 脚本会自动计算最新的交易日期，无需手动设置")
        print("💾 运行前会自动备份原文件，运行后恢复")
        print()

def main():
    """主函数"""
    checker = TimeSettingChecker()
    checker.generate_report()

if __name__ == "__main__":
    main()
