#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据清理脚本
提供常用的数据清理操作，简化操作流程
"""

import sys
import time
from sqlalchemy import create_engine, text
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuickCleaner:
    """快速清理工具"""
    
    def __init__(self):
        self.db_configs = {
            'qtdb': "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4",
            'index': "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4", 
            'ind': "mysql+pymysql://root:12345678@localhost:3306/ind?charset=utf8mb4",
            'Daily Line': "mysql+pymysql://root:12345678@localhost:3306/Daily%20Line?charset=utf8mb4"
        }
    
    def confirm_action(self, message):
        """简单确认"""
        print(f"\n⚠️  {message}")
        response = input("确认执行? (y/n): ").strip().lower()
        return response in ['y', 'yes', '是']
    
    def execute_sql(self, db_name, sql, description):
        """执行SQL语句"""
        try:
            engine = create_engine(self.db_configs[db_name])
            with engine.connect() as conn:
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                conn.execute(text(sql))
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                conn.commit()
            
            logger.info(f"✅ {description}")
            return True
        except Exception as e:
            logger.error(f"❌ {description} 失败: {e}")
            return False
    
    def clear_all_stock_daily_data(self):
        """清空所有股票日线数据"""
        if not self.confirm_action("清空所有股票日线数据表"):
            return
        
        print("🧹 开始清理股票日线数据...")
        
        # 清理qtdb中的股票日线表
        sql = """
        SELECT CONCAT('TRUNCATE TABLE `', table_name, '`;') as truncate_sql
        FROM information_schema.tables 
        WHERE table_schema = 'qtdb' 
        AND table_name LIKE 'stock_daily_%'
        """
        
        try:
            engine = create_engine(self.db_configs['qtdb'])
            with engine.connect() as conn:
                result = conn.execute(text(sql))
                truncate_statements = [row[0] for row in result]
                
                if not truncate_statements:
                    print("❌ 未找到股票日线数据表")
                    return
                
                print(f"📋 找到 {len(truncate_statements)} 个股票日线表")
                
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                for stmt in truncate_statements:
                    conn.execute(text(stmt))
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                conn.commit()
                
                print(f"✅ 成功清空 {len(truncate_statements)} 个股票日线表")
                
        except Exception as e:
            logger.error(f"❌ 清空股票日线数据失败: {e}")
    
    def clear_all_financial_data(self):
        """清空所有财务数据"""
        if not self.confirm_action("清空所有财务数据表（资产负债表、利润表、现金流量表）"):
            return
        
        print("🧹 开始清理财务数据...")
        
        table_patterns = ['balance_sheet_%', 'income_statement_%', 'cash_flow_%']
        
        for pattern in table_patterns:
            sql = f"""
            SELECT CONCAT('TRUNCATE TABLE `', table_name, '`;') as truncate_sql
            FROM information_schema.tables 
            WHERE table_schema = 'qtdb' 
            AND table_name LIKE '{pattern}'
            """
            
            try:
                engine = create_engine(self.db_configs['qtdb'])
                with engine.connect() as conn:
                    result = conn.execute(text(sql))
                    truncate_statements = [row[0] for row in result]
                    
                    if truncate_statements:
                        print(f"📋 清理 {pattern.replace('%', '')} 表: {len(truncate_statements)} 个")
                        
                        conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                        for stmt in truncate_statements:
                            conn.execute(text(stmt))
                        conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                        conn.commit()
                        
                        print(f"✅ 成功清空 {len(truncate_statements)} 个表")
                    
            except Exception as e:
                logger.error(f"❌ 清空 {pattern} 表失败: {e}")
    
    def clear_all_dividend_data(self):
        """清空所有分红数据"""
        if not self.confirm_action("清空所有分红数据表"):
            return
        
        print("🧹 开始清理分红数据...")
        
        sql = """
        SELECT CONCAT('TRUNCATE TABLE `', table_name, '`;') as truncate_sql
        FROM information_schema.tables 
        WHERE table_schema = 'qtdb' 
        AND table_name LIKE 'dividend_%'
        """
        
        try:
            engine = create_engine(self.db_configs['qtdb'])
            with engine.connect() as conn:
                result = conn.execute(text(sql))
                truncate_statements = [row[0] for row in result]
                
                if not truncate_statements:
                    print("❌ 未找到分红数据表")
                    return
                
                print(f"📋 找到 {len(truncate_statements)} 个分红数据表")
                
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                for stmt in truncate_statements:
                    conn.execute(text(stmt))
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                conn.commit()
                
                print(f"✅ 成功清空 {len(truncate_statements)} 个分红数据表")
                
        except Exception as e:
            logger.error(f"❌ 清空分红数据失败: {e}")
    
    def clear_all_index_data(self):
        """清空所有指数数据"""
        if not self.confirm_action("清空所有指数数据"):
            return
        
        print("🧹 开始清理指数数据...")
        
        # 清理index数据库
        for db_name in ['index', 'ind']:
            if db_name in self.db_configs:
                try:
                    engine = create_engine(self.db_configs[db_name])
                    with engine.connect() as conn:
                        # 获取所有表
                        result = conn.execute(text("SHOW TABLES"))
                        tables = [row[0] for row in result]
                        
                        if tables:
                            print(f"📋 清理 {db_name} 数据库: {len(tables)} 个表")
                            
                            conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                            for table in tables:
                                conn.execute(text(f"TRUNCATE TABLE `{table}`"))
                            conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                            conn.commit()
                            
                            print(f"✅ 成功清空 {db_name} 数据库的 {len(tables)} 个表")
                        
                except Exception as e:
                    logger.error(f"❌ 清空 {db_name} 数据库失败: {e}")
    
    def clear_everything(self):
        """清空所有数据"""
        print("\n" + "="*60)
        print("⚠️  危险操作：清空所有数据")
        print("="*60)
        print("这将清空以下所有数据:")
        print("- 股票日线数据")
        print("- 财务数据（资产负债表、利润表、现金流量表）")
        print("- 分红数据")
        print("- 指数数据")
        print("\n❗ 此操作不可逆，请谨慎确认！")
        
        # 多重确认
        response1 = input("\n确认清空所有数据? (yes/no): ").strip().lower()
        if response1 != 'yes':
            print("❌ 操作已取消")
            return
        
        response2 = input("请输入 'CLEAR ALL' 来最终确认: ").strip()
        if response2 != 'CLEAR ALL':
            print("❌ 操作已取消")
            return
        
        print("\n⏰ 3秒后开始执行...")
        try:
            for i in range(3, 0, -1):
                print(f"{i}...")
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
            return
        
        print("\n🧹 开始清空所有数据...")
        
        self.clear_all_stock_daily_data()
        self.clear_all_financial_data()
        self.clear_all_dividend_data()
        self.clear_all_index_data()
        
        print("\n🎉 所有数据清理完成！")

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🧹 快速数据清理工具")
    print("="*50)
    print("1. 清空股票日线数据")
    print("2. 清空财务数据")
    print("3. 清空分红数据")
    print("4. 清空指数数据")
    print("5. 清空所有数据 (危险)")
    print("0. 退出")
    print("="*50)

def main():
    """主函数"""
    cleaner = QuickCleaner()
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                cleaner.clear_all_stock_daily_data()
            elif choice == '2':
                cleaner.clear_all_financial_data()
            elif choice == '3':
                cleaner.clear_all_dividend_data()
            elif choice == '4':
                cleaner.clear_all_index_data()
            elif choice == '5':
                cleaner.clear_everything()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            logger.error(f"操作错误: {e}")

if __name__ == "__main__":
    main()
