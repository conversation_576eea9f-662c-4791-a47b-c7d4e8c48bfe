#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库备份脚本
在删除数据前进行备份，确保数据安全
"""

import os
import subprocess
import time
from datetime import datetime
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_backup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseBackup:
    """数据库备份工具"""
    
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': '12345678'
        }
        
        self.databases = ['qtdb', 'index', 'ind', 'Daily Line']
        self.backup_dir = Path('database_backups')
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
    
    def get_mysqldump_path(self):
        """获取mysqldump路径"""
        # 常见的mysqldump路径
        possible_paths = [
            'mysqldump',  # 如果在PATH中
            'C:/Program Files/MySQL/MySQL Server 8.0/bin/mysqldump.exe',
            'C:/Program Files/MySQL/MySQL Server 5.7/bin/mysqldump.exe',
            'C:/xampp/mysql/bin/mysqldump.exe',
            '/usr/bin/mysqldump',
            '/usr/local/bin/mysqldump'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"找到mysqldump: {path}")
                    return path
            except FileNotFoundError:
                continue
        
        logger.error("未找到mysqldump工具")
        return None
    
    def backup_database(self, db_name, backup_type='full'):
        """备份单个数据库"""
        mysqldump_path = self.get_mysqldump_path()
        if not mysqldump_path:
            return False
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{db_name}_{backup_type}_{timestamp}.sql"
        backup_path = self.backup_dir / backup_filename
        
        # 构建mysqldump命令
        cmd = [
            mysqldump_path,
            f"--host={self.mysql_config['host']}",
            f"--port={self.mysql_config['port']}",
            f"--user={self.mysql_config['user']}",
            f"--password={self.mysql_config['password']}",
            '--single-transaction',
            '--routines',
            '--triggers',
            '--default-character-set=utf8mb4'
        ]
        
        if backup_type == 'structure_only':
            cmd.append('--no-data')
        elif backup_type == 'data_only':
            cmd.append('--no-create-info')
        
        cmd.append(db_name)
        
        try:
            logger.info(f"开始备份数据库: {db_name}")
            start_time = time.time()
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                elapsed_time = time.time() - start_time
                file_size = backup_path.stat().st_size / (1024 * 1024)  # MB
                
                logger.info(f"✅ 备份成功: {backup_filename}")
                logger.info(f"   文件大小: {file_size:.2f} MB")
                logger.info(f"   耗时: {elapsed_time:.1f} 秒")
                return True
            else:
                logger.error(f"❌ 备份失败: {result.stderr}")
                # 删除失败的备份文件
                if backup_path.exists():
                    backup_path.unlink()
                return False
                
        except Exception as e:
            logger.error(f"❌ 备份异常: {e}")
            return False
    
    def backup_all_databases(self, backup_type='full'):
        """备份所有数据库"""
        print(f"\n🗄️  开始备份所有数据库 ({backup_type})")
        print("="*50)
        
        success_count = 0
        total_start_time = time.time()
        
        for db_name in self.databases:
            if self.backup_database(db_name, backup_type):
                success_count += 1
            else:
                print(f"⚠️  跳过数据库: {db_name}")
        
        total_elapsed = time.time() - total_start_time
        
        print(f"\n📊 备份完成")
        print(f"成功备份: {success_count}/{len(self.databases)} 个数据库")
        print(f"总耗时: {total_elapsed:.1f} 秒")
        print(f"备份目录: {self.backup_dir.absolute()}")
        
        return success_count == len(self.databases)
    
    def list_backups(self):
        """列出所有备份文件"""
        backup_files = list(self.backup_dir.glob('*.sql'))
        
        if not backup_files:
            print("❌ 没有找到备份文件")
            return
        
        print(f"\n📋 备份文件列表 ({len(backup_files)} 个文件)")
        print("="*80)
        
        # 按时间排序
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for backup_file in backup_files:
            stat = backup_file.stat()
            size_mb = stat.st_size / (1024 * 1024)
            mtime = datetime.fromtimestamp(stat.st_mtime)
            
            print(f"📄 {backup_file.name}")
            print(f"   大小: {size_mb:.2f} MB")
            print(f"   时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
    
    def restore_database(self, backup_file, target_db=None):
        """恢复数据库"""
        backup_path = self.backup_dir / backup_file
        
        if not backup_path.exists():
            logger.error(f"备份文件不存在: {backup_file}")
            return False
        
        # 从文件名提取数据库名
        if not target_db:
            target_db = backup_file.split('_')[0]
        
        print(f"\n⚠️  恢复数据库")
        print(f"备份文件: {backup_file}")
        print(f"目标数据库: {target_db}")
        print("❗ 这将覆盖目标数据库的所有数据！")
        
        response = input("确认恢复? (yes/no): ").strip().lower()
        if response != 'yes':
            print("❌ 恢复已取消")
            return False
        
        # 构建mysql命令
        cmd = [
            'mysql',
            f"--host={self.mysql_config['host']}",
            f"--port={self.mysql_config['port']}",
            f"--user={self.mysql_config['user']}",
            f"--password={self.mysql_config['password']}",
            '--default-character-set=utf8mb4',
            target_db
        ]
        
        try:
            logger.info(f"开始恢复数据库: {target_db}")
            start_time = time.time()
            
            with open(backup_path, 'r', encoding='utf-8') as f:
                result = subprocess.run(cmd, stdin=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                elapsed_time = time.time() - start_time
                logger.info(f"✅ 恢复成功，耗时: {elapsed_time:.1f} 秒")
                return True
            else:
                logger.error(f"❌ 恢复失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 恢复异常: {e}")
            return False
    
    def clean_old_backups(self, keep_days=7):
        """清理旧备份文件"""
        backup_files = list(self.backup_dir.glob('*.sql'))
        current_time = time.time()
        old_files = []
        
        for backup_file in backup_files:
            file_age_days = (current_time - backup_file.stat().st_mtime) / (24 * 3600)
            if file_age_days > keep_days:
                old_files.append(backup_file)
        
        if not old_files:
            print(f"❌ 没有超过 {keep_days} 天的备份文件")
            return
        
        print(f"\n📋 找到 {len(old_files)} 个超过 {keep_days} 天的备份文件:")
        total_size = 0
        for old_file in old_files:
            size_mb = old_file.stat().st_size / (1024 * 1024)
            total_size += size_mb
            mtime = datetime.fromtimestamp(old_file.stat().st_mtime)
            print(f"  📄 {old_file.name} ({size_mb:.2f} MB, {mtime.strftime('%Y-%m-%d')})")
        
        print(f"总大小: {total_size:.2f} MB")
        
        response = input(f"\n确认删除这些备份文件? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            try:
                for old_file in old_files:
                    old_file.unlink()
                    logger.info(f"已删除: {old_file.name}")
                
                print(f"✅ 成功删除 {len(old_files)} 个旧备份文件")
            except Exception as e:
                logger.error(f"❌ 删除备份文件失败: {e}")
        else:
            print("❌ 清理已取消")

def main():
    """主函数"""
    backup_tool = DatabaseBackup()
    
    while True:
        print("\n" + "="*50)
        print("🗄️  数据库备份工具")
        print("="*50)
        print("1. 完整备份所有数据库")
        print("2. 仅备份数据库结构")
        print("3. 仅备份数据")
        print("4. 查看备份文件")
        print("5. 恢复数据库")
        print("6. 清理旧备份")
        print("0. 退出")
        print("="*50)
        
        try:
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                backup_tool.backup_all_databases('full')
            elif choice == '2':
                backup_tool.backup_all_databases('structure_only')
            elif choice == '3':
                backup_tool.backup_all_databases('data_only')
            elif choice == '4':
                backup_tool.list_backups()
            elif choice == '5':
                backup_tool.list_backups()
                if input("\n继续恢复操作? (y/n): ").strip().lower() in ['y', 'yes']:
                    backup_file = input("请输入备份文件名: ").strip()
                    backup_tool.restore_database(backup_file)
            elif choice == '6':
                days = input("保留多少天的备份? (默认7天): ").strip()
                keep_days = int(days) if days.isdigit() else 7
                backup_tool.clean_old_backups(keep_days)
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            logger.error(f"操作错误: {e}")

if __name__ == "__main__":
    main()
