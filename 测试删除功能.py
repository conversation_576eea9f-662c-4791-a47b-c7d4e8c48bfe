#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能
验证删除脚本的各项功能是否正常
"""

import sys
from pathlib import Path

def test_import():
    """测试脚本导入"""
    try:
        from 删除数据库数据 import DatabaseCleaner
        from 快速清理数据 import QuickCleaner
        from 数据库备份 import DatabaseBackup
        print("✅ 所有脚本导入成功")
        return True
    except ImportError as e:
        print(f"❌ 脚本导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from 删除数据库数据 import DatabaseCleaner
        cleaner = DatabaseCleaner()
        
        if cleaner.test_connection():
            print("✅ 数据库连接成功")
            
            # 获取数据库列表
            databases = cleaner.get_database_list()
            print(f"📋 找到数据库: {databases}")
            
            # 测试获取表列表
            for db_name in ['qtdb', 'index', 'ind']:
                if db_name in cleaner.db_configs:
                    tables = cleaner.get_table_list(db_name)
                    print(f"📊 {db_name} 数据库有 {len(tables)} 个表")
            
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接测试异常: {e}")
        return False

def test_backup_tool():
    """测试备份工具"""
    try:
        from 数据库备份 import DatabaseBackup
        backup_tool = DatabaseBackup()
        
        # 检查mysqldump
        mysqldump_path = backup_tool.get_mysqldump_path()
        if mysqldump_path:
            print(f"✅ 找到备份工具: {mysqldump_path}")
            return True
        else:
            print("⚠️  未找到mysqldump工具，备份功能可能不可用")
            return False
            
    except Exception as e:
        print(f"❌ 备份工具测试异常: {e}")
        return False

def test_file_existence():
    """测试文件是否存在"""
    required_files = [
        '删除数据库数据.py',
        '快速清理数据.py', 
        '数据库备份.py',
        '数据库管理说明.md'
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} (文件不存在)")
            all_exist = False
    
    return all_exist

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("📖 数据库管理工具使用指南")
    print("="*60)
    
    print("\n🗄️ 备份数据（推荐先执行）:")
    print("python 数据库备份.py")
    
    print("\n🗑️ 删除数据:")
    print("python 删除数据库数据.py    # 详细删除工具（推荐）")
    print("python 快速清理数据.py      # 快速批量清理")
    
    print("\n🚀 重新获取数据:")
    print("python 一键启动.py")
    
    print("\n📋 查看详细说明:")
    print("查看 数据库管理说明.md 文件")
    
    print("\n⚠️ 安全提示:")
    print("1. 删除前务必备份数据")
    print("2. 仔细确认删除范围")
    print("3. 使用详细删除工具进行精确操作")
    print("4. 检查日志文件了解操作结果")

def main():
    """主函数"""
    print("🧪 数据库删除功能测试")
    print("="*50)
    
    # 测试文件存在性
    print("\n📁 检查文件:")
    files_ok = test_file_existence()
    
    # 测试脚本导入
    print("\n📦 测试脚本导入:")
    import_ok = test_import()
    
    # 测试数据库连接
    print("\n🔗 测试数据库连接:")
    db_ok = test_database_connection()
    
    # 测试备份工具
    print("\n🗄️ 测试备份工具:")
    backup_ok = test_backup_tool()
    
    # 测试结果总结
    print("\n📊 测试结果:")
    print(f"文件检查: {'✅ 通过' if files_ok else '❌ 失败'}")
    print(f"脚本导入: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"数据库连接: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"备份工具: {'✅ 通过' if backup_ok else '⚠️ 部分功能不可用'}")
    
    if files_ok and import_ok and db_ok:
        print("\n🎉 所有核心功能测试通过！")
        print("✅ 可以安全使用数据库管理工具")
        show_usage_guide()
    else:
        print("\n⚠️ 部分功能测试失败")
        print("请检查相关问题后再使用")
        
        if not db_ok:
            print("\n🔧 数据库连接问题排查:")
            print("1. 检查MySQL服务是否运行")
            print("2. 验证数据库用户名密码")
            print("3. 确认数据库权限设置")

if __name__ == "__main__":
    main()
