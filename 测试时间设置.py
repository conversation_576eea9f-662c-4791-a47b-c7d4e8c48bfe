#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间设置功能
验证自动日期计算和脚本修改功能
"""

from datetime import datetime, timedelta
from pathlib import Path

def get_latest_trading_date():
    """获取最新的交易日期"""
    now = datetime.now()
    
    # 如果是周末，调整到最近的交易日
    if now.weekday() == 5:  # 周六
        now = now - timedelta(days=1)
    elif now.weekday() == 6:  # 周日
        now = now - timedelta(days=2)
    
    # 如果是当天且时间早于15:30，使用前一个交易日
    if now.hour < 15 or (now.hour == 15 and now.minute < 30):
        now = now - timedelta(days=1)
        # 再次检查是否是周末
        if now.weekday() == 5:  # 周六
            now = now - timedelta(days=1)
        elif now.weekday() == 6:  # 周日
            now = now - timedelta(days=2)
    
    return now.strftime('%Y%m%d')

def check_scripts_exist():
    """检查所有脚本文件是否存在"""
    scripts = [
        '全量股票日线.py',
        '指数全量.py', 
        'tushare_index_data.py',
        '财务数据分开表格全量更新.py',
        '分红数据分开表格全量更新.py'
    ]
    
    print("📋 检查脚本文件:")
    all_exist = True
    
    for script in scripts:
        script_path = Path(script)
        if script_path.exists():
            print(f"✅ {script}")
        else:
            print(f"❌ {script} (文件不存在)")
            all_exist = False
    
    return all_exist

def test_date_calculation():
    """测试日期计算功能"""
    print("\n📅 测试日期计算:")
    
    # 获取最新交易日期
    end_date = get_latest_trading_date()
    start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=30)).strftime('%Y%m%d')
    
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前是星期: {['一', '二', '三', '四', '五', '六', '日'][datetime.now().weekday()]}")
    print(f"计算的最新交易日: {end_date}")
    print(f"30天前的日期: {start_date}")
    
    # 验证日期格式
    try:
        datetime.strptime(end_date, '%Y%m%d')
        datetime.strptime(start_date, '%Y%m%d')
        print("✅ 日期格式正确")
    except ValueError:
        print("❌ 日期格式错误")
        return False
    
    return True

def test_script_modification():
    """测试脚本修改功能"""
    print("\n🔧 测试脚本修改功能:")
    
    script_path = Path('全量股票日线.py')
    
    if not script_path.exists():
        print(f"❌ 测试脚本不存在: {script_path}")
        return False
    
    try:
        # 读取原文件
        with open(script_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 检查是否包含目标函数调用
        if 'result = fetch_all_stocks_daily_data()' in original_content:
            print("✅ 找到目标函数调用")
        else:
            print("⚠️  未找到目标函数调用，脚本可能已被修改")
        
        # 检查是否支持日期参数
        if 'start_date=None, end_date=None' in original_content:
            print("✅ 脚本支持日期参数")
        else:
            print("❌ 脚本不支持日期参数")
            return False
        
        print("✅ 脚本修改功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 读取脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 时间设置功能测试")
    print("="*50)
    
    # 检查脚本文件
    scripts_ok = check_scripts_exist()
    
    # 测试日期计算
    date_ok = test_date_calculation()
    
    # 测试脚本修改
    modification_ok = test_script_modification()
    
    # 总结
    print("\n📊 测试结果:")
    print(f"脚本文件检查: {'✅ 通过' if scripts_ok else '❌ 失败'}")
    print(f"日期计算功能: {'✅ 通过' if date_ok else '❌ 失败'}")
    print(f"脚本修改功能: {'✅ 通过' if modification_ok else '❌ 失败'}")
    
    if scripts_ok and date_ok and modification_ok:
        print("\n🎉 所有测试通过！可以使用 '一键启动.py' 来运行所有脚本")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")
    
    print("\n💡 使用说明:")
    print("1. 运行 'python 一键启动.py' 来启动所有脚本")
    print("2. 脚本会自动计算最新的交易日期")
    print("3. 对于支持日期参数的脚本，会自动设置日期范围")
    print("4. 执行完成后会自动恢复原始脚本")

if __name__ == "__main__":
    main()
