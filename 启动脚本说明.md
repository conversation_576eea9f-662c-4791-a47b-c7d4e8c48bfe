# 启动脚本使用说明

本项目提供了多个启动脚本，适用于不同的数据获取需求。

## 📋 启动脚本对比

| 脚本名称 | 数据范围 | 适用场景 | 执行时间 |
|---------|---------|---------|---------|
| `全量数据启动.py` | **全量历史数据** | 首次部署、重建数据库 | 6-12小时 |
| `一键启动.py` | 可选择范围 | 灵活使用、日常更新 | 可变 |
| 单独脚本 | 各自默认范围 | 单独更新某类数据 | 可变 |

## 🎯 推荐使用方案

### 1. 首次获取全量数据（推荐）
```bash
python 全量数据启动.py
```
**特点：**
- ✅ 获取所有可用的历史数据
- ✅ 无日期限制，数据最完整
- ✅ 专为全量设计，流程优化
- ⚠️ 耗时较长（6-12小时）

### 2. 灵活选择数据范围
```bash
python 一键启动.py
```
**选项：**
1. **全量数据** - 获取所有历史数据
2. **增量数据** - 最近30天数据
3. **自定义范围** - 指定日期范围

### 3. 单独运行特定脚本
```bash
# 只获取指数数据
python 指数全量.py

# 只获取股票日线数据
python 全量股票日线.py

# 只获取财务数据
python 财务数据分开表格全量更新.py
```

## 📊 数据获取范围说明

### 全量数据脚本的默认行为

| 脚本 | 默认数据范围 | 说明 |
|-----|-------------|------|
| `指数全量.py` | 全量历史数据 | 获取指数从上市到最新的所有数据 |
| `tushare_index_data.py` | 全量历史数据 | 获取指数成分股的所有历史变化 |
| `全量股票日线.py` | 全量历史数据* | 当 start_date=None 时获取全量 |
| `财务数据分开表格全量更新.py` | 全量历史数据 | 获取所有可用的财务报表 |
| `分红数据分开表格全量更新.py` | 全量历史数据 | 获取所有分红派息记录 |

*注：`全量股票日线.py` 支持日期参数，但默认为全量

## ⚠️ 重要说明

### 关于"全量"的理解
- **全量** = 获取该股票/指数从上市日期到最新交易日的所有数据
- **不是** = 只获取最近30天的数据

### 为什么之前显示30天范围？
在 `一键启动.py` 中，为了给用户选择，默认设置了30天的增量模式。但实际上：

1. **`全量数据启动.py`** - 专门为全量设计，不设置任何日期限制
2. **原始脚本** - 默认就是获取全量数据
3. **`一键启动.py`** - 提供选择，可以选择全量模式

## 🚀 推荐执行流程

### 首次部署
```bash
# 1. 清理现有数据（可选）
python 快速清理数据.py

# 2. 获取全量数据
python 全量数据启动.py
```

### 日常更新
```bash
# 选择增量更新
python 一键启动.py
# 选择选项 2（增量数据）
```

### 重建特定数据
```bash
# 只重建股票数据
python 全量股票日线.py

# 只重建财务数据  
python 财务数据分开表格全量更新.py
```

## 📈 数据量预估

### 全量数据大小（估算）
- **股票日线数据**: ~50GB
- **财务数据**: ~20GB  
- **分红数据**: ~5GB
- **指数数据**: ~1GB
- **总计**: ~76GB

### 执行时间（估算）
- **指数数据**: 5-10分钟
- **股票日线**: 3-6小时
- **财务数据**: 2-4小时
- **分红数据**: 30-60分钟
- **总计**: 6-12小时

## 💡 优化建议

### 提高执行效率
1. **网络环境**: 确保网络稳定，避免频繁重试
2. **数据库配置**: 适当调整MySQL配置提高写入性能
3. **分批执行**: 可以分别执行不同类型的数据获取
4. **监控进度**: 关注日志输出，及时发现问题

### 避免常见问题
1. **API限制**: 脚本已内置限流，无需担心
2. **磁盘空间**: 确保有足够空间存储数据
3. **执行中断**: 可以重新运行，脚本支持增量更新
4. **数据质量**: 定期检查数据完整性

## 🔧 故障排除

### 如果执行失败
1. **检查网络连接**
2. **验证数据库连接**
3. **查看错误日志**
4. **重新运行失败的脚本**

### 如果数据不完整
1. **运行检查脚本**: `python 检查数据清理结果.py`
2. **重新运行特定脚本**
3. **检查API配额是否用完**

## 📞 技术支持

如果遇到问题：
1. 查看相关日志文件
2. 检查数据库连接状态  
3. 验证网络连接
4. 确认API token有效性
