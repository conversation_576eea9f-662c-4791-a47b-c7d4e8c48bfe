#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动所有数据获取脚本
自动设置最新时间，按顺序执行所有脚本
"""

import os
import sys
import subprocess
import time
from datetime import datetime, timedelta
from pathlib import Path

def get_latest_trading_date():
    """获取最新的交易日期"""
    now = datetime.now()
    
    # 如果是周末，调整到最近的交易日
    if now.weekday() == 5:  # 周六
        now = now - timedelta(days=1)
    elif now.weekday() == 6:  # 周日
        now = now - timedelta(days=2)
    
    # 如果是当天且时间早于15:30，使用前一个交易日
    if now.hour < 15 or (now.hour == 15 and now.minute < 30):
        now = now - timedelta(days=1)
        # 再次检查是否是周末
        if now.weekday() == 5:  # 周六
            now = now - timedelta(days=1)
        elif now.weekday() == 6:  # 周日
            now = now - timedelta(days=2)
    
    return now.strftime('%Y%m%d')

def modify_stock_daily_script(start_date, end_date):
    """修改全量股票日线.py脚本，添加日期参数"""
    script_path = Path('全量股票日线.py')
    
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    try:
        # 读取原文件
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = Path('全量股票日线.py.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 修改函数调用，添加日期参数
        if 'result = fetch_all_stocks_daily_data()' in content:
            content = content.replace(
                'result = fetch_all_stocks_daily_data()',
                f'result = fetch_all_stocks_daily_data(start_date="{start_date}", end_date="{end_date}")'
            )
            
            # 保存修改后的文件
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已设置日期范围: {start_date} 到 {end_date}")
            return True
        else:
            print("⚠️  未找到需要修改的函数调用")
            return False
            
    except Exception as e:
        print(f"❌ 修改脚本失败: {e}")
        return False

def restore_stock_daily_script():
    """恢复全量股票日线.py脚本的原始版本"""
    script_path = Path('全量股票日线.py')
    backup_path = Path('全量股票日线.py.backup')
    
    try:
        if backup_path.exists():
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            backup_path.unlink()  # 删除备份文件
            print("✅ 已恢复脚本原始版本")
    except Exception as e:
        print(f"❌ 恢复脚本失败: {e}")

def run_script(script_name, description, estimated_time):
    """运行单个脚本"""
    script_path = Path(script_name)
    
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_name}")
        return False
    
    print(f"\n{'='*50}")
    print(f"🚀 开始执行: {script_name}")
    print(f"📝 描述: {description}")
    print(f"⏱️  预计耗时: {estimated_time}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, str(script_path)],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {script_name} 执行成功")
            print(f"⏱️  实际耗时: {elapsed_time:.1f}秒")
            if result.stdout:
                print("📋 输出信息:")
                print(result.stdout[-500:])  # 显示最后500个字符
            return True
        else:
            print(f"❌ {script_name} 执行失败")
            print(f"⏱️  耗时: {elapsed_time:.1f}秒")
            if result.stderr:
                print("❗ 错误信息:")
                print(result.stderr[-500:])  # 显示最后500个字符
            return False
            
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ {script_name} 执行异常: {e}")
        print(f"⏱️  耗时: {elapsed_time:.1f}秒")
        return False

def main():
    """主函数"""
    print("🎯 一键启动所有数据获取脚本")
    print("="*60)
    
    # 获取最新交易日期
    end_date = get_latest_trading_date()
    start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=30)).strftime('%Y%m%d')
    
    print(f"📅 自动设置日期范围: {start_date} 到 {end_date}")
    print(f"🕐 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 脚本列表
    scripts = [
        {
            'name': '指数全量.py',
            'description': '获取所有指数的历史数据',
            'estimated_time': '5分钟'
        },
        {
            'name': 'tushare_index_data.py',
            'description': '获取指数成分股和变化率数据',
            'estimated_time': '10分钟'
        },
        {
            'name': '全量股票日线.py',
            'description': '获取所有股票的日线数据和基本指标',
            'estimated_time': '2-4小时'
        },
        {
            'name': '财务数据分开表格全量更新.py',
            'description': '获取所有股票的财务报表数据',
            'estimated_time': '1-2小时'
        },
        {
            'name': '分红数据分开表格全量更新.py',
            'description': '获取所有股票的分红派息数据',
            'estimated_time': '30分钟'
        }
    ]
    
    # 显示执行计划
    print("\n📋 执行计划:")
    for i, script in enumerate(scripts, 1):
        print(f"{i}. {script['name']} - {script['description']} (预计{script['estimated_time']})")
    
    # 询问用户确认
    try:
        response = input("\n❓ 是否继续执行所有脚本? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("❌ 用户取消执行")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断执行")
        return
    
    # 开始执行
    total_start_time = time.time()
    success_count = 0
    failed_scripts = []
    
    for i, script in enumerate(scripts, 1):
        print(f"\n📊 执行进度: {i}/{len(scripts)}")
        
        # 特殊处理全量股票日线.py脚本
        if script['name'] == '全量股票日线.py':
            if modify_stock_daily_script(start_date, end_date):
                success = run_script(script['name'], script['description'], script['estimated_time'])
                restore_stock_daily_script()
            else:
                print(f"❌ 无法修改 {script['name']} 的日期设置，跳过执行")
                success = False
        else:
            success = run_script(script['name'], script['description'], script['estimated_time'])
        
        if success:
            success_count += 1
        else:
            failed_scripts.append(script['name'])
            
            # 询问是否继续
            try:
                response = input(f"\n❓ {script['name']} 执行失败，是否继续执行下一个脚本? (y/n): ").strip().lower()
                if response not in ['y', 'yes', '是']:
                    print("❌ 用户选择停止执行")
                    break
            except KeyboardInterrupt:
                print("\n❌ 用户中断执行")
                break
    
    # 执行总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print("📊 执行总结")
    print(f"{'='*60}")
    print(f"⏱️  总耗时: {total_elapsed:.1f}秒 ({total_elapsed/60:.1f}分钟)")
    print(f"✅ 成功执行: {success_count}/{len(scripts)} 个脚本")
    
    if failed_scripts:
        print(f"❌ 失败的脚本: {', '.join(failed_scripts)}")
    else:
        print("🎉 所有脚本执行成功!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
