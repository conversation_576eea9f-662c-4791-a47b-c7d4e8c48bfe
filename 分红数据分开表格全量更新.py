import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text
import logging
from datetime import datetime
import time
import re
import numpy as np
import os
from typing import List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dividend_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration - Use environment variables for security
TUSHARE_TOKEN = os.getenv("TUSHARE_TOKEN", "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211")
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4")

# Initialize
try:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    logger.info("Tushare API initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Tushare API: {e}")
    raise

def test_database_connection():
    """Test database connection"""
    try:
        engine = create_engine(DATABASE_URL, pool_recycle=3600, pool_pre_ping=True)
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            # Test Chinese character support
            conn.execute(text("SELECT '测试中文' as test_chinese"))
            logger.info("Database connection successful with UTF-8 support")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

def create_database():
    """Create database if it doesn't exist"""
    try:
        # Extract database name from URL (handle charset parameter)
        db_name = DATABASE_URL.split('/')[-1].split('?')[0]  # Remove charset parameter
        base_url = DATABASE_URL.rsplit('/', 1)[0]

        temp_engine = create_engine(base_url)
        with temp_engine.connect() as conn:
            # Create database with UTF-8 charset
            conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
        logger.info(f"Database '{db_name}' created or already exists with UTF-8 support")
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False

def get_all_stocks():
    """Get all stock codes"""
    try:
        logger.info("Fetching all stock codes...")
        stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        logger.info(f"Found {len(stock_basic)} stocks")
        return stock_basic['ts_code'].tolist()
    except Exception as e:
        logger.error(f"Error fetching stock codes: {e}")
        raise

def clean_table_name(ts_code):
    """Convert stock code to valid table name"""
    return re.sub(r'[^\w]', '_', ts_code.lower())

def clean_dataframe(df):
    """Clean dataframe for MySQL compatibility"""
    if df.empty:
        return df

    # Replace NaN with None for MySQL compatibility
    df = df.replace({np.nan: None})

    # Convert date columns to proper format
    date_columns = ['end_date', 'ann_date', 'record_date', 'ex_date', 'pay_date', 'div_listdate', 'imp_ann_date', 'base_date']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')

    # Ensure string columns are properly encoded
    string_columns = ['div_proc']  # Add other string columns as needed
    for col in string_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).replace('nan', None)

    return df

def fetch_all_dividend_data():
    """Fetch dividend data for all stocks - separate table per stock"""

    # Test database connection first
    if not test_database_connection():
        logger.error("Cannot proceed without database connection")
        return False

    if not create_database():
        logger.error("Failed to create database")
        return False

    engine = create_engine(DATABASE_URL, pool_recycle=3600, pool_pre_ping=True,
                          connect_args={"charset": "utf8mb4", "use_unicode": True})

    # Get all stock codes
    try:
        stock_codes = get_all_stocks()
    except Exception as e:
        logger.error(f"Failed to get stock codes: {e}")
        return False

    success_count = 0
    error_count = 0

    for i, stock_code in enumerate(stock_codes):
        logger.info(f"Processing {stock_code} ({i+1}/{len(stock_codes)})")

        try:
            table_suffix = clean_table_name(stock_code)

            # Dividend data
            dividend_df = pro.dividend(
                ts_code=stock_code,
                fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date,div_listdate,imp_ann_date,base_date,base_share'
            )

            if not dividend_df.empty:
                # Clean the dataframe
                dividend_df = clean_dataframe(dividend_df)

                # Use context manager for database connection
                with engine.connect() as conn:
                    # Create table with proper charset first
                    table_name = f'dividend_{table_suffix}'
                    conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))

                    dividend_df.to_sql(table_name, conn, if_exists='replace', index=False, method='multi')

                    # Alter table to ensure proper charset
                    conn.execute(text(f"ALTER TABLE {table_name} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                logger.info(f"  Dividend: {len(dividend_df)} records saved")
                success_count += 1
            else:
                logger.info(f"  Dividend: No data for {stock_code}")

            # Rate limiting - avoid hitting API limits
            time.sleep(0.2)

        except Exception as e:
            logger.error(f"Error processing {stock_code}: {e}")
            error_count += 1
            continue

    logger.info(f"Processing completed. Success: {success_count}, Errors: {error_count}")
    return True

def fetch_combined_dividend_data():
    """Fetch dividend data for all stocks - single combined table"""
    
    create_database()
    engine = create_engine(DATABASE_URL, pool_recycle=3600, pool_pre_ping=True)
    
    # Get all stock codes
    stock_codes = get_all_stocks()
    all_dividend_data = []
    
    for i, stock_code in enumerate(stock_codes):
        print(f"Processing {stock_code} ({i+1}/{len(stock_codes)})")
        
        try:
            # Dividend data
            dividend_df = pro.dividend(
                ts_code=stock_code,
                fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date,div_listdate,imp_ann_date,base_date,base_share'
            )
            
            if not dividend_df.empty:
                dividend_df = clean_dataframe(dividend_df)
                all_dividend_data.append(dividend_df)
                print(f"  Dividend: {len(dividend_df)} records")
            else:
                print(f"  Dividend: No data")
                
            # Rate limiting - avoid hitting API limits
            time.sleep(0.2)
            
        except Exception as e:
            print(f"Error processing {stock_code}: {e}")
            continue
    
    # Combine all dividend data
    if all_dividend_data:
        print("Combining and storing dividend data...")
        combined_dividend = pd.concat(all_dividend_data, ignore_index=True)
        
        with engine.connect() as conn:
            combined_dividend.to_sql('all_dividend_data', conn, if_exists='replace', index=False, method='multi')
        print(f"All dividend data: {len(combined_dividend)} records stored")
    else:
        print("No dividend data found")

def main():
    """Main function with proper error handling"""
    logger.info("Starting dividend data fetch process")

    try:
        # Test basic functionality first
        logger.info("Testing API connection...")
        test_stocks = get_all_stocks()[:5]  # Test with first 5 stocks
        logger.info(f"API test successful. Found {len(test_stocks)} test stocks")

        # Choose one of the following:

        # Option 1: Separate table per stock
        success = fetch_all_dividend_data()

        # Option 2: Single combined table (uncomment to use)
        # success = fetch_combined_dividend_data()

        if success:
            logger.info("Process completed successfully")
        else:
            logger.error("Process completed with errors")

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":
    main()
