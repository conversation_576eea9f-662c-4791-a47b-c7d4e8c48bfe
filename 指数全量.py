import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text
import logging

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def create_database_if_not_exists():
    """Create the index database if it doesn't exist"""
    base_url = "mysql+pymysql://root:12345678@localhost:3306/?charset=utf8mb4"
    engine = create_engine(base_url)
    
    with engine.connect() as conn:
        conn.execute(text("CREATE DATABASE IF NOT EXISTS `index`"))
        conn.commit()
    
    engine.dispose()

def fetch_and_store_index_data():
    """Fetch all index data and store in database"""
    # Create database if not exists
    create_database_if_not_exists()
    
    # Connect to the index database
    index_db_url = "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4"
    engine = create_engine(index_db_url)
    
    # List of all indices to fetch
    indices = [
        '000001.SH',  # sh000001 - Shanghai Composite Index
        '000016.SH',  # sh000016 - SSE 50 Index
        '000300.SH',  # sh000300 - CSI 300 Index
        '000852.SH',  # sh000852 - CSI 1000 Index
        '000905.SH',  # sh000905 - CSI 500 Index
        '932000.CSI', # CSI 2000 Index - 中证2000指数
        '399001.SZ',  # sz399001 - Shenzhen Component Index (替代932000.SH)
        '399006.SZ'   # sz399006 - ChiNext Index
    ]
    
    for ts_code in indices:
        try:
            print(f"Fetching data for {ts_code}...")
            
            # Fetch all available index data
            df = pro.index_daily(ts_code=ts_code)
            
            # Rename columns to match your requirements
            df = df.rename(columns={
                'trade_date': 'candle_end_time',
                'ts_code': 'index_code',
                'vol': 'volume'
            })
            
            # Select required columns
            df = df[['candle_end_time', 'open', 'high', 'low', 'close', 'amount', 'volume', 'index_code']]
            
            # Convert trade_date to datetime format
            df['candle_end_time'] = pd.to_datetime(df['candle_end_time'])
            
            # Create table name based on index code
            table_name = ts_code.lower().replace('.', '')
            
            # Store data to database
            df.to_sql(table_name, engine, if_exists='replace', index=False)
            
            print(f"Successfully stored {len(df)} records for {ts_code} in table {table_name}")
            
        except Exception as e:
            logging.error(f"Error fetching/storing data for {ts_code}: {e}")
    
    engine.dispose()

if __name__ == "__main__":
    fetch_and_store_index_data()
