# 一键启动数据获取脚本

这个项目提供了一套完整的股票和指数数据获取脚本，支持自动时间设置和一键启动所有脚本。

## 📋 脚本列表

1. **指数全量.py** - 获取所有指数的历史数据 (预计5分钟)
2. **tushare_index_data.py** - 获取指数成分股和变化率数据 (预计10分钟)
3. **全量股票日线.py** - 获取所有股票的日线数据和基本指标 (预计2-4小时)
4. **财务数据分开表格全量更新.py** - 获取所有股票的财务报表数据 (预计1-2小时)
5. **分红数据分开表格全量更新.py** - 获取所有股票的分红派息数据 (预计30分钟)

## 🚀 快速开始

### 方法一：一键启动所有脚本（推荐）

```bash
python 一键启动.py
```

这个脚本会：
- ✅ 自动计算最新的交易日期
- ✅ 自动设置日期范围（最近30天）
- ✅ 按顺序执行所有脚本
- ✅ 显示执行进度和结果
- ✅ 自动备份和恢复脚本文件

### 方法二：单独运行脚本

```bash
# 运行指数数据获取
python 指数全量.py

# 运行股票日线数据获取
python 全量股票日线.py

# 运行财务数据获取
python 财务数据分开表格全量更新.py

# 运行分红数据获取
python 分红数据分开表格全量更新.py

# 运行指数成分股数据获取
python tushare_index_data.py
```

## 🔧 辅助工具

### 检查时间设置

```bash
python 检查时间设置.py
```

显示所有脚本的时间设置状态和建议。

### 测试功能

```bash
python 测试时间设置.py
```

测试自动时间计算和脚本修改功能是否正常。

## ⏰ 时间设置说明

### 自动时间计算规则

脚本会自动计算最新的交易日期：

1. **工作日（周一到周五）**：
   - 如果当前时间在15:30之前，使用前一个交易日
   - 如果当前时间在15:30之后，使用当天

2. **周末**：
   - 周六：使用周五
   - 周日：使用周五

3. **日期范围**：
   - 结束日期：最新交易日
   - 开始日期：结束日期前30天

### 当前设置示例

```
当前时间: 2025-08-09 23:27:45 (星期六)
计算的最新交易日: 20250808 (周五)
30天前的日期: 20250709
```

## 📊 数据库配置

所有脚本使用以下数据库配置：

```python
# 主数据库
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"

# 指数数据库
INDEX_DB_URL = "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4"

# 指数成分股数据库
IND_DB_URL = "mysql+pymysql://root:12345678@localhost:3306/ind?charset=utf8mb4"
```

## 🔑 API配置

使用Tushare API获取数据：

```python
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
```

## 📈 数据内容

### 股票日线数据
- 基础价格数据（开高低收、成交量、成交额）
- 技术指标（换手率、市盈率、市净率等）
- 重新加权价格数据

### 指数数据
- 指数历史价格数据
- 指数成分股信息
- 指数变化率数据

### 财务数据
- 资产负债表
- 利润表
- 现金流量表

### 分红数据
- 分红派息记录
- 除权除息日期
- 分红比例和金额

## ⚠️ 注意事项

1. **API限制**：脚本内置了速率限制，避免超过Tushare API限制
2. **执行时间**：全量数据获取需要较长时间，建议在网络稳定时运行
3. **数据库空间**：确保数据库有足够的存储空间
4. **备份**：脚本会自动备份修改的文件，执行完成后恢复

## 🛠️ 故障排除

### 常见问题

1. **脚本执行失败**
   - 检查网络连接
   - 验证数据库连接
   - 确认Tushare API token有效

2. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证数据库用户名和密码
   - 确认数据库权限

3. **API限制错误**
   - 脚本会自动重试
   - 如果频繁出现，可以增加延迟时间

### 日志文件

脚本会生成以下日志文件：
- `master_script.log` - 主启动脚本日志
- `stock_data_fetch.log` - 股票数据获取日志
- `dividend_fetch.log` - 分红数据获取日志

## 📞 支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接状态
3. 网络连接状态
4. API token有效性

## 🎯 使用建议

1. **首次运行**：建议先运行测试脚本确认环境正常
2. **定期更新**：可以设置定时任务自动运行
3. **监控执行**：关注日志文件，及时发现问题
4. **数据验证**：定期检查数据完整性和准确性
