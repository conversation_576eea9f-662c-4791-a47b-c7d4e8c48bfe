#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库数据删除脚本
支持安全删除各种数据表和数据库
包含多重确认机制，防止误删除
"""

import os
import sys
import time
from datetime import datetime
from sqlalchemy import create_engine, text, inspect
import pymysql
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_cleanup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseCleaner:
    """数据库清理工具"""
    
    def __init__(self):
        # 数据库配置
        self.db_configs = {
            'qtdb': "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4",
            'index': "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4", 
            'ind': "mysql+pymysql://root:12345678@localhost:3306/ind?charset=utf8mb4",
            'Daily Line': "mysql+pymysql://root:12345678@localhost:3306/Daily%20Line?charset=utf8mb4"
        }
        self.base_url = "mysql+pymysql://root:12345678@localhost:3306?charset=utf8mb4"
    
    def test_connection(self, db_name=None):
        """测试数据库连接"""
        try:
            if db_name:
                engine = create_engine(self.db_configs[db_name])
            else:
                engine = create_engine(self.base_url)
            
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info(f"✅ 数据库连接成功: {db_name or 'MySQL服务器'}")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def get_database_list(self):
        """获取所有数据库列表"""
        try:
            engine = create_engine(self.base_url)
            with engine.connect() as conn:
                result = conn.execute(text("SHOW DATABASES"))
                databases = [row[0] for row in result if row[0] not in 
                           ['information_schema', 'performance_schema', 'mysql', 'sys']]
            return databases
        except Exception as e:
            logger.error(f"获取数据库列表失败: {e}")
            return []
    
    def get_table_list(self, db_name):
        """获取指定数据库的表列表"""
        try:
            if db_name not in self.db_configs:
                logger.error(f"未知的数据库: {db_name}")
                return []
            
            engine = create_engine(self.db_configs[db_name])
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            return tables
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            return []
    
    def get_table_info(self, db_name, table_name):
        """获取表的详细信息"""
        try:
            engine = create_engine(self.db_configs[db_name])
            with engine.connect() as conn:
                # 获取表行数
                result = conn.execute(text(f"SELECT COUNT(*) FROM `{table_name}`"))
                row_count = result.scalar()
                
                # 获取表大小
                result = conn.execute(text(f"""
                    SELECT 
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = '{db_name}' AND table_name = '{table_name}'
                """))
                size_mb = result.scalar() or 0
                
                return {
                    'rows': row_count,
                    'size_mb': size_mb
                }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {'rows': 0, 'size_mb': 0}
    
    def confirm_deletion(self, operation_type, target_name, details=None):
        """多重确认删除操作"""
        print(f"\n{'='*60}")
        print(f"⚠️  危险操作确认")
        print(f"{'='*60}")
        print(f"操作类型: {operation_type}")
        print(f"目标: {target_name}")
        
        if details:
            print(f"详细信息:")
            for key, value in details.items():
                print(f"  - {key}: {value}")
        
        print(f"\n❗ 此操作不可逆，请仔细确认！")
        
        # 第一次确认
        response1 = input(f"\n确认要{operation_type} '{target_name}' 吗? (yes/no): ").strip().lower()
        if response1 not in ['yes', 'y']:
            print("❌ 操作已取消")
            return False
        
        # 第二次确认
        print(f"\n⚠️  最后确认：您即将{operation_type} '{target_name}'")
        response2 = input("请输入 'DELETE' 来确认删除: ").strip()
        if response2 != 'DELETE':
            print("❌ 操作已取消")
            return False
        
        # 倒计时
        print("\n⏰ 3秒后开始执行，按Ctrl+C取消...")
        try:
            for i in range(3, 0, -1):
                print(f"{i}...")
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
            return False
        
        return True
    
    def delete_table_data(self, db_name, table_name):
        """删除表中的所有数据（保留表结构）"""
        if not self.test_connection(db_name):
            return False
        
        # 获取表信息
        table_info = self.get_table_info(db_name, table_name)
        
        # 确认删除
        if not self.confirm_deletion(
            "清空表数据", 
            f"{db_name}.{table_name}",
            {
                "数据行数": f"{table_info['rows']:,}",
                "表大小": f"{table_info['size_mb']} MB",
                "操作": "删除所有数据，保留表结构"
            }
        ):
            return False
        
        try:
            engine = create_engine(self.db_configs[db_name])
            with engine.connect() as conn:
                # 禁用外键检查
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                
                # 清空表数据
                conn.execute(text(f"TRUNCATE TABLE `{table_name}`"))
                
                # 重新启用外键检查
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                
                conn.commit()
            
            logger.info(f"✅ 成功清空表数据: {db_name}.{table_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清空表数据失败: {e}")
            return False
    
    def drop_table(self, db_name, table_name):
        """删除整个表"""
        if not self.test_connection(db_name):
            return False
        
        # 获取表信息
        table_info = self.get_table_info(db_name, table_name)
        
        # 确认删除
        if not self.confirm_deletion(
            "删除表", 
            f"{db_name}.{table_name}",
            {
                "数据行数": f"{table_info['rows']:,}",
                "表大小": f"{table_info['size_mb']} MB",
                "操作": "删除表结构和所有数据"
            }
        ):
            return False
        
        try:
            engine = create_engine(self.db_configs[db_name])
            with engine.connect() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS `{table_name}`"))
                conn.commit()
            
            logger.info(f"✅ 成功删除表: {db_name}.{table_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除表失败: {e}")
            return False
    
    def drop_database(self, db_name):
        """删除整个数据库"""
        if not self.test_connection():
            return False
        
        # 获取数据库信息
        tables = self.get_table_list(db_name)
        
        # 确认删除
        if not self.confirm_deletion(
            "删除数据库", 
            db_name,
            {
                "表数量": len(tables),
                "操作": "删除整个数据库和所有数据"
            }
        ):
            return False
        
        try:
            engine = create_engine(self.base_url)
            with engine.connect() as conn:
                conn.execute(text(f"DROP DATABASE IF EXISTS `{db_name}`"))
                conn.commit()
            
            logger.info(f"✅ 成功删除数据库: {db_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除数据库失败: {e}")
            return False
    
    def clean_all_stock_tables(self, db_name):
        """清理所有股票相关的表"""
        if not self.test_connection(db_name):
            return False
        
        tables = self.get_table_list(db_name)
        stock_tables = [t for t in tables if any(prefix in t for prefix in 
                       ['stock_daily_', 'balance_sheet_', 'income_statement_', 
                        'cash_flow_', 'dividend_'])]
        
        if not stock_tables:
            print("❌ 未找到股票相关的表")
            return False
        
        print(f"\n📋 找到 {len(stock_tables)} 个股票相关的表:")
        for i, table in enumerate(stock_tables[:10], 1):
            print(f"  {i}. {table}")
        if len(stock_tables) > 10:
            print(f"  ... 还有 {len(stock_tables) - 10} 个表")
        
        # 确认删除
        if not self.confirm_deletion(
            "批量清空股票表数据", 
            f"{db_name} 数据库中的 {len(stock_tables)} 个股票表",
            {
                "表数量": len(stock_tables),
                "操作": "清空所有股票相关表的数据，保留表结构"
            }
        ):
            return False
        
        success_count = 0
        for table in stock_tables:
            try:
                engine = create_engine(self.db_configs[db_name])
                with engine.connect() as conn:
                    conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                    conn.execute(text(f"TRUNCATE TABLE `{table}`"))
                    conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                    conn.commit()
                
                success_count += 1
                logger.info(f"✅ 已清空: {table}")
                
            except Exception as e:
                logger.error(f"❌ 清空失败: {table} - {e}")
        
        logger.info(f"✅ 批量清理完成: {success_count}/{len(stock_tables)} 个表")
        return success_count > 0

    def show_database_overview(self):
        """显示数据库概览"""
        print("\n📊 数据库概览")
        print("="*60)

        databases = self.get_database_list()

        for db_name in databases:
            if db_name in self.db_configs:
                print(f"\n📁 数据库: {db_name}")
                print("-" * 40)

                if self.test_connection(db_name):
                    tables = self.get_table_list(db_name)
                    print(f"表数量: {len(tables)}")

                    # 显示前几个表的信息
                    for table in tables[:5]:
                        info = self.get_table_info(db_name, table)
                        print(f"  📋 {table}: {info['rows']:,} 行, {info['size_mb']} MB")

                    if len(tables) > 5:
                        print(f"  ... 还有 {len(tables) - 5} 个表")
                else:
                    print("❌ 连接失败")

    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "="*60)
            print("🗑️  数据库数据删除工具")
            print("="*60)
            print("1. 查看数据库概览")
            print("2. 清空表数据（保留表结构）")
            print("3. 删除整个表")
            print("4. 删除整个数据库")
            print("5. 批量清理股票表数据")
            print("6. 清理日志文件")
            print("0. 退出")
            print("="*60)

            try:
                choice = input("请选择操作 (0-6): ").strip()

                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    self.show_database_overview()
                elif choice == '2':
                    self.menu_clear_table_data()
                elif choice == '3':
                    self.menu_drop_table()
                elif choice == '4':
                    self.menu_drop_database()
                elif choice == '5':
                    self.menu_batch_clean_stock_tables()
                elif choice == '6':
                    self.clean_log_files()
                else:
                    print("❌ 无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                logger.error(f"菜单操作错误: {e}")

    def menu_clear_table_data(self):
        """清空表数据菜单"""
        print("\n📋 选择数据库:")
        databases = [db for db in self.db_configs.keys()]

        for i, db in enumerate(databases, 1):
            print(f"{i}. {db}")

        try:
            db_choice = int(input("请选择数据库 (数字): ")) - 1
            if 0 <= db_choice < len(databases):
                db_name = databases[db_choice]

                tables = self.get_table_list(db_name)
                if not tables:
                    print("❌ 该数据库中没有表")
                    return

                print(f"\n📋 {db_name} 数据库中的表:")
                for i, table in enumerate(tables, 1):
                    info = self.get_table_info(db_name, table)
                    print(f"{i}. {table} ({info['rows']:,} 行, {info['size_mb']} MB)")

                table_choice = int(input("请选择要清空的表 (数字): ")) - 1
                if 0 <= table_choice < len(tables):
                    table_name = tables[table_choice]
                    self.delete_table_data(db_name, table_name)
                else:
                    print("❌ 无效的表选择")
            else:
                print("❌ 无效的数据库选择")
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            logger.error(f"清空表数据菜单错误: {e}")

    def menu_drop_table(self):
        """删除表菜单"""
        print("\n📋 选择数据库:")
        databases = [db for db in self.db_configs.keys()]

        for i, db in enumerate(databases, 1):
            print(f"{i}. {db}")

        try:
            db_choice = int(input("请选择数据库 (数字): ")) - 1
            if 0 <= db_choice < len(databases):
                db_name = databases[db_choice]

                tables = self.get_table_list(db_name)
                if not tables:
                    print("❌ 该数据库中没有表")
                    return

                print(f"\n📋 {db_name} 数据库中的表:")
                for i, table in enumerate(tables, 1):
                    info = self.get_table_info(db_name, table)
                    print(f"{i}. {table} ({info['rows']:,} 行, {info['size_mb']} MB)")

                table_choice = int(input("请选择要删除的表 (数字): ")) - 1
                if 0 <= table_choice < len(tables):
                    table_name = tables[table_choice]
                    self.drop_table(db_name, table_name)
                else:
                    print("❌ 无效的表选择")
            else:
                print("❌ 无效的数据库选择")
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            logger.error(f"删除表菜单错误: {e}")

    def menu_drop_database(self):
        """删除数据库菜单"""
        databases = self.get_database_list()

        print("\n📋 可删除的数据库:")
        for i, db in enumerate(databases, 1):
            print(f"{i}. {db}")

        try:
            db_choice = int(input("请选择要删除的数据库 (数字): ")) - 1
            if 0 <= db_choice < len(databases):
                db_name = databases[db_choice]
                self.drop_database(db_name)
            else:
                print("❌ 无效的数据库选择")
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            logger.error(f"删除数据库菜单错误: {e}")

    def menu_batch_clean_stock_tables(self):
        """批量清理股票表菜单"""
        print("\n📋 选择要清理股票表的数据库:")
        databases = [db for db in self.db_configs.keys()]

        for i, db in enumerate(databases, 1):
            print(f"{i}. {db}")

        try:
            db_choice = int(input("请选择数据库 (数字): ")) - 1
            if 0 <= db_choice < len(databases):
                db_name = databases[db_choice]
                self.clean_all_stock_tables(db_name)
            else:
                print("❌ 无效的数据库选择")
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            logger.error(f"批量清理菜单错误: {e}")

    def clean_log_files(self):
        """清理日志文件"""
        log_files = [
            'database_cleanup.log',
            'master_script.log',
            'stock_data_fetch.log',
            'dividend_fetch.log'
        ]

        existing_logs = [f for f in log_files if os.path.exists(f)]

        if not existing_logs:
            print("❌ 没有找到日志文件")
            return

        print(f"\n📋 找到 {len(existing_logs)} 个日志文件:")
        for log_file in existing_logs:
            size = os.path.getsize(log_file) / 1024  # KB
            print(f"  📄 {log_file} ({size:.1f} KB)")

        if self.confirm_deletion("删除日志文件", f"{len(existing_logs)} 个日志文件"):
            try:
                for log_file in existing_logs:
                    os.remove(log_file)
                    logger.info(f"✅ 已删除日志文件: {log_file}")
                print(f"✅ 成功删除 {len(existing_logs)} 个日志文件")
            except Exception as e:
                logger.error(f"❌ 删除日志文件失败: {e}")

def main():
    """主函数"""
    print("🗑️  数据库数据删除工具")
    print("⚠️  请谨慎使用，所有删除操作不可逆！")

    cleaner = DatabaseCleaner()

    # 测试数据库连接
    if not cleaner.test_connection():
        print("❌ 无法连接到数据库，请检查配置")
        return

    # 启动交互式菜单
    cleaner.interactive_menu()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        print(f"❌ 程序异常: {e}")
