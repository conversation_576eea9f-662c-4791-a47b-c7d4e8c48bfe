# 数据库管理工具使用说明

本项目提供了完整的数据库管理工具，包括数据获取、备份、清理和删除功能。

## 📋 工具列表

### 🚀 数据获取工具
- **`一键启动.py`** - 自动获取所有数据
- **`检查时间设置.py`** - 检查脚本时间配置
- **`测试时间设置.py`** - 测试功能是否正常

### 🗄️ 数据备份工具
- **`数据库备份.py`** - 完整的数据库备份和恢复功能

### 🗑️ 数据清理工具
- **`删除数据库数据.py`** - 详细的数据删除工具（推荐）
- **`快速清理数据.py`** - 快速批量清理工具

## 🔄 推荐的工作流程

### 1. 首次使用
```bash
# 1. 测试环境
python 测试时间设置.py

# 2. 获取数据
python 一键启动.py
```

### 2. 定期维护
```bash
# 1. 备份数据（推荐）
python 数据库备份.py

# 2. 清理旧数据
python 快速清理数据.py

# 3. 获取新数据
python 一键启动.py
```

### 3. 故障恢复
```bash
# 1. 从备份恢复
python 数据库备份.py

# 2. 重新获取数据
python 一键启动.py
```

## 🗄️ 数据库备份工具

### 功能特性
- ✅ 支持完整备份、结构备份、数据备份
- ✅ 自动压缩和时间戳命名
- ✅ 备份文件管理和清理
- ✅ 一键恢复功能

### 使用方法
```bash
python 数据库备份.py
```

### 菜单选项
1. **完整备份所有数据库** - 备份结构和数据
2. **仅备份数据库结构** - 只备份表结构
3. **仅备份数据** - 只备份数据内容
4. **查看备份文件** - 列出所有备份
5. **恢复数据库** - 从备份恢复
6. **清理旧备份** - 删除过期备份

### 备份文件命名规则
```
数据库名_备份类型_时间戳.sql
例如: qtdb_full_20250809_232745.sql
```

## 🗑️ 数据删除工具

### 详细删除工具 (`删除数据库数据.py`)

**推荐使用** - 提供最安全的删除操作

#### 功能特性
- ✅ 多重确认机制
- ✅ 详细的数据信息显示
- ✅ 支持单表、批量、数据库级别删除
- ✅ 完整的操作日志

#### 使用方法
```bash
python 删除数据库数据.py
```

#### 菜单选项
1. **查看数据库概览** - 显示所有数据库和表的信息
2. **清空表数据** - 删除数据，保留表结构
3. **删除整个表** - 删除表结构和数据
4. **删除整个数据库** - 删除整个数据库
5. **批量清理股票表数据** - 批量清理股票相关表
6. **清理日志文件** - 清理程序日志

#### 安全机制
- 🔒 三重确认：选择确认 → 输入确认 → 倒计时确认
- 📊 显示详细信息：表大小、行数、影响范围
- 📝 完整日志记录
- ⚠️ 清晰的警告提示

### 快速清理工具 (`快速清理数据.py`)

**适合熟练用户** - 提供快速批量操作

#### 功能特性
- ⚡ 快速批量清理
- 🎯 按数据类型分类清理
- 🧹 一键清空所有数据

#### 使用方法
```bash
python 快速清理数据.py
```

#### 菜单选项
1. **清空股票日线数据** - 清空所有 `stock_daily_*` 表
2. **清空财务数据** - 清空资产负债表、利润表、现金流量表
3. **清空分红数据** - 清空所有 `dividend_*` 表
4. **清空指数数据** - 清空指数和成分股数据
5. **清空所有数据** - 清空所有数据（危险操作）

## 📊 数据库结构

### 主数据库 (qtdb)
```
股票日线数据: stock_daily_*
财务数据:
  - 资产负债表: balance_sheet_*
  - 利润表: income_statement_*
  - 现金流量表: cash_flow_*
分红数据: dividend_*
```

### 指数数据库 (index)
```
指数历史数据: 各指数代码对应的表
```

### 指数成分股数据库 (ind)
```
指数成分股数据: *_index 表
```

### 日线数据库 (Daily Line)
```
股票日线数据: stock_daily_* (另一个版本)
```

## ⚠️ 安全注意事项

### 删除操作前
1. **务必备份数据**
   ```bash
   python 数据库备份.py
   ```

2. **确认删除范围**
   - 使用详细删除工具查看数据信息
   - 确认要删除的具体内容

3. **选择合适的删除方式**
   - 清空数据：保留表结构，便于重新导入
   - 删除表：完全删除，需要重新创建
   - 删除数据库：最危险，慎重使用

### 删除操作中
1. **仔细阅读确认信息**
2. **按要求输入确认文本**
3. **利用倒计时时间最后检查**

### 删除操作后
1. **检查日志文件**
   ```
   database_cleanup.log - 删除操作日志
   ```

2. **验证删除结果**
   - 使用数据库概览功能检查
   - 确认删除是否符合预期

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   检查MySQL服务是否运行
   验证用户名密码是否正确
   确认数据库权限设置
   ```

2. **备份失败**
   ```
   检查mysqldump工具是否安装
   确认备份目录权限
   检查磁盘空间是否充足
   ```

3. **删除操作失败**
   ```
   检查外键约束
   确认表是否被锁定
   验证用户权限
   ```

### 日志文件
- `database_cleanup.log` - 删除操作日志
- `database_backup.log` - 备份操作日志
- `master_script.log` - 数据获取日志

## 💡 最佳实践

1. **定期备份**
   - 每次大量删除前备份
   - 定期清理旧备份文件

2. **渐进式清理**
   - 先清理单个表测试
   - 再进行批量操作

3. **监控日志**
   - 定期检查日志文件
   - 及时发现和解决问题

4. **测试环境**
   - 在测试环境先验证操作
   - 确认无误后在生产环境执行

## 📞 技术支持

如遇问题，请：
1. 检查相关日志文件
2. 确认数据库连接状态
3. 验证操作权限
4. 查看错误信息详情
