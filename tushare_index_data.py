import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text
import pymysql

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# Index codes mapping
INDEX_CODES = {
    '000001.SH': 'sh000001_index',  # 上证指数
    '000016.SH': 'sh000016_index',  # 上证50
    '000300.SH': 'sh000300_index',  # 沪深300
    '000852.SH': 'sh000852_index',  # 中证1000
    '000905.SH': 'sh000905_index',  # 中证500
    '399001.SZ': 'sz399001_index',  # 深证成指
    '399006.SZ': 'sz399006_index',  # 创业板指
    '932000.CSI': 'csi932000_index'  # 中证2000
}

def create_database_if_not_exists():
    """Create 'ind' database if it doesn't exist"""
    base_url = "mysql+pymysql://root:12345678@localhost:3306/?charset=utf8mb4"
    engine = create_engine(base_url)
    
    with engine.connect() as conn:
        conn.execute(text("CREATE DATABASE IF NOT EXISTS ind"))
    
    engine.dispose()

def get_index_constituents(index_code):
    """Retrieve index constituents for a specific index"""
    try:
        df_constituents = pro.index_weight(
            index_code=index_code
        )
        
        if df_constituents.empty:
            return pd.DataFrame(columns=['transaction_date', 'component_codes'])
        
        constituents_grouped = df_constituents.groupby('trade_date')['con_code'].apply(
            lambda x: '  '.join(x)
        ).reset_index()
        
        constituents_grouped.rename(columns={
            'trade_date': 'transaction_date',
            'con_code': 'component_codes'
        }, inplace=True)
        
        return constituents_grouped
    except Exception as e:
        print(f"Warning: Could not retrieve constituents for {index_code}: {e}")
        return pd.DataFrame(columns=['transaction_date', 'component_codes'])

def get_index_data(index_code):
    """Retrieve index data for a specific index with component codes"""
    # Get all historical index daily data
    df = pro.index_daily(
        ts_code=index_code
    )
    
    # Select required columns
    df = df[['trade_date', 'ts_code', 'pct_chg']].copy()
    df.rename(columns={
        'trade_date': 'transaction_date', 
        'ts_code': 'index_code',
        'pct_chg': 'change_rate'
    }, inplace=True)
    
    # Get constituents data
    constituents = get_index_constituents(index_code)
    
    # Merge with constituents data
    df = df.merge(constituents, on='transaction_date', how='left')
    
    # Forward fill missing component codes
    df['component_codes'] = df['component_codes'].fillna(method='ffill')
    
    # Remove rows where component_codes is still null
    df = df.dropna(subset=['component_codes'])
    
    return df

def store_to_database(df, table_name):
    """Store data to ind database"""
    db_url = "mysql+pymysql://root:12345678@localhost:3306/ind?charset=utf8mb4"
    engine = create_engine(db_url)
    
    df.to_sql(table_name, engine, if_exists='replace', index=False)
    engine.dispose()

if __name__ == "__main__":
    # Create database
    create_database_if_not_exists()
    
    # Process each index
    for index_code, table_name in INDEX_CODES.items():
        try:
            print(f"Processing {index_code}...")
            data = get_index_data(index_code)
            print(f"Retrieved {len(data)} records for {index_code}")
            
            # Store data
            store_to_database(data, table_name)
            print(f"Data stored successfully in table '{table_name}'")
            
        except Exception as e:
            print(f"Error processing {index_code}: {e}")
    
    print("All indices processed successfully!")


