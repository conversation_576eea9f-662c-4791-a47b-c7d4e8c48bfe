#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据清理结果
验证表中的数据是否已被清空
"""

from sqlalchemy import create_engine, text
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataChecker:
    """数据检查工具"""
    
    def __init__(self):
        self.db_configs = {
            'qtdb': "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4",
            'index': "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4", 
            'ind': "mysql+pymysql://root:12345678@localhost:3306/ind?charset=utf8mb4",
            'Daily Line': "mysql+pymysql://root:12345678@localhost:3306/Daily%20Line?charset=utf8mb4"
        }
    
    def check_table_data(self, db_name, table_pattern=None):
        """检查表中的数据行数"""
        try:
            engine = create_engine(self.db_configs[db_name])
            with engine.connect() as conn:
                # 获取表列表
                if table_pattern:
                    sql = f"""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = '{db_name}' 
                    AND table_name LIKE '{table_pattern}'
                    ORDER BY table_name
                    """
                else:
                    sql = f"""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = '{db_name}'
                    ORDER BY table_name
                    """
                
                result = conn.execute(text(sql))
                tables = [row[0] for row in result]
                
                if not tables:
                    print(f"❌ {db_name} 数据库中没有找到匹配的表")
                    return
                
                print(f"\n📊 {db_name} 数据库检查结果:")
                print("-" * 60)
                
                total_tables = 0
                empty_tables = 0
                total_rows = 0
                
                for table in tables:
                    try:
                        # 获取行数
                        count_result = conn.execute(text(f"SELECT COUNT(*) FROM `{table}`"))
                        row_count = count_result.scalar()
                        
                        total_tables += 1
                        total_rows += row_count
                        
                        if row_count == 0:
                            empty_tables += 1
                            status = "✅ 已清空"
                        else:
                            status = f"⚠️  有数据 ({row_count:,} 行)"
                        
                        print(f"📋 {table:<30} {status}")
                        
                    except Exception as e:
                        print(f"❌ {table:<30} 检查失败: {e}")
                
                print("-" * 60)
                print(f"📊 总计: {total_tables} 个表, {empty_tables} 个已清空, 总数据行数: {total_rows:,}")
                
                if empty_tables == total_tables:
                    print("🎉 所有表都已清空!")
                elif empty_tables > 0:
                    print(f"⚠️  {total_tables - empty_tables} 个表仍有数据")
                else:
                    print("❌ 没有表被清空")
                
        except Exception as e:
            logger.error(f"检查 {db_name} 数据库失败: {e}")
    
    def check_all_databases(self):
        """检查所有数据库"""
        print("🔍 检查所有数据库的数据清理结果")
        print("=" * 80)
        
        # 检查qtdb中的股票相关表
        print("\n🏢 股票日线数据:")
        self.check_table_data('qtdb', 'stock_daily_%')
        
        print("\n💰 财务数据:")
        print("  资产负债表:")
        self.check_table_data('qtdb', 'balance_sheet_%')
        print("  利润表:")
        self.check_table_data('qtdb', 'income_statement_%')
        print("  现金流量表:")
        self.check_table_data('qtdb', 'cash_flow_%')
        
        print("\n💸 分红数据:")
        self.check_table_data('qtdb', 'dividend_%')
        
        print("\n📈 指数数据:")
        self.check_table_data('index')
        
        print("\n📊 指数成分股数据:")
        self.check_table_data('ind')
    
    def check_specific_tables(self):
        """检查特定类型的表"""
        while True:
            print("\n" + "="*50)
            print("🔍 数据检查工具")
            print("="*50)
            print("1. 检查股票日线数据")
            print("2. 检查财务数据")
            print("3. 检查分红数据")
            print("4. 检查指数数据")
            print("5. 检查所有数据")
            print("0. 退出")
            print("="*50)
            
            try:
                choice = input("请选择检查类型 (0-5): ").strip()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    self.check_table_data('qtdb', 'stock_daily_%')
                elif choice == '2':
                    print("资产负债表:")
                    self.check_table_data('qtdb', 'balance_sheet_%')
                    print("\n利润表:")
                    self.check_table_data('qtdb', 'income_statement_%')
                    print("\n现金流量表:")
                    self.check_table_data('qtdb', 'cash_flow_%')
                elif choice == '3':
                    self.check_table_data('qtdb', 'dividend_%')
                elif choice == '4':
                    print("指数历史数据:")
                    self.check_table_data('index')
                    print("\n指数成分股数据:")
                    self.check_table_data('ind')
                elif choice == '5':
                    self.check_all_databases()
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                logger.error(f"操作错误: {e}")

def main():
    """主函数"""
    checker = DataChecker()
    
    print("🔍 数据清理结果检查工具")
    print("=" * 60)
    print("说明:")
    print("- TRUNCATE TABLE 只清空数据，不删除表结构")
    print("- 表格仍然存在是正常的")
    print("- 重点检查表中是否还有数据")
    print("=" * 60)
    
    # 先做一次快速检查
    print("\n🚀 快速检查所有数据库...")
    checker.check_all_databases()
    
    # 提供交互式检查
    print("\n" + "="*60)
    response = input("是否需要进入交互式检查模式? (y/n): ").strip().lower()
    if response in ['y', 'yes', '是']:
        checker.check_specific_tables()

if __name__ == "__main__":
    main()
