#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动所有数据获取脚本
自动设置最新时间，并按顺序执行所有脚本
"""

import os
import sys
import subprocess
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
import threading
import queue

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('master_script.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScriptRunner:
    """脚本运行器类"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.scripts = [
            {
                'name': '指数全量数据',
                'file': '指数全量.py',
                'description': '获取所有指数的历史数据',
                'estimated_time': '5分钟'
            },
            {
                'name': 'Tushare指数数据',
                'file': 'tushare_index_data.py', 
                'description': '获取指数成分股和变化率数据',
                'estimated_time': '10分钟'
            },
            {
                'name': '全量股票日线数据',
                'file': '全量股票日线.py',
                'description': '获取所有股票的日线数据和基本指标',
                'estimated_time': '2-4小时'
            },
            {
                'name': '财务数据更新',
                'file': '财务数据分开表格全量更新.py',
                'description': '获取所有股票的财务报表数据',
                'estimated_time': '1-2小时'
            },
            {
                'name': '分红数据更新',
                'file': '分红数据分开表格全量更新.py',
                'description': '获取所有股票的分红派息数据',
                'estimated_time': '30分钟'
            }
        ]
        
    def check_scripts_exist(self):
        """检查所有脚本文件是否存在"""
        missing_scripts = []
        for script in self.scripts:
            script_path = self.current_dir / script['file']
            if not script_path.exists():
                missing_scripts.append(script['file'])
        
        if missing_scripts:
            logger.error(f"以下脚本文件不存在: {missing_scripts}")
            return False
        
        logger.info("所有脚本文件检查完成 ✓")
        return True
    
    def get_date_range(self):
        """获取日期范围 - 自动设置为最新"""
        # 获取当前日期
        end_date = datetime.now()
        
        # 如果是周末，调整到最近的交易日
        if end_date.weekday() == 5:  # 周六
            end_date = end_date - timedelta(days=1)
        elif end_date.weekday() == 6:  # 周日
            end_date = end_date - timedelta(days=2)
        
        # 如果是当天且时间早于15:30，使用前一个交易日
        if end_date.hour < 15 or (end_date.hour == 15 and end_date.minute < 30):
            end_date = end_date - timedelta(days=1)
            # 再次检查是否是周末
            if end_date.weekday() == 5:  # 周六
                end_date = end_date - timedelta(days=1)
            elif end_date.weekday() == 6:  # 周日
                end_date = end_date - timedelta(days=2)
        
        # 设置开始日期为30天前（可以根据需要调整）
        start_date = end_date - timedelta(days=30)
        
        # 格式化为YYYYMMDD
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')
        
        logger.info(f"自动设置日期范围: {start_date_str} 到 {end_date_str}")
        return start_date_str, end_date_str
    
    def modify_script_dates(self, script_file, start_date, end_date):
        """修改脚本中的日期设置"""
        script_path = self.current_dir / script_file

        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 备份原文件
            backup_path = script_path.with_suffix('.py.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # 修改全量股票日线.py中的函数调用
            if script_file == '全量股票日线.py':
                # 查找主函数调用并添加日期参数
                if 'result = fetch_all_stocks_daily_data()' in content:
                    content = content.replace(
                        'result = fetch_all_stocks_daily_data()',
                        f'result = fetch_all_stocks_daily_data(start_date="{start_date}", end_date="{end_date}")'
                    )
                    logger.info(f"已修改 {script_file} 的日期参数")

            # 保存修改后的文件
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return True

        except Exception as e:
            logger.error(f"修改脚本 {script_file} 失败: {e}")
            return False

    def restore_script_backup(self, script_file):
        """恢复脚本备份"""
        script_path = self.current_dir / script_file
        backup_path = script_path.with_suffix('.py.backup')

        try:
            if backup_path.exists():
                with open(backup_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                backup_path.unlink()  # 删除备份文件
                logger.info(f"已恢复 {script_file} 的原始版本")
        except Exception as e:
            logger.error(f"恢复脚本 {script_file} 失败: {e}")

    def run_script(self, script_info, start_date=None, end_date=None):
        """运行单个脚本"""
        script_name = script_info['name']
        script_file = script_info['file']
        script_path = self.current_dir / script_file

        logger.info(f"开始执行: {script_name}")
        logger.info(f"脚本描述: {script_info['description']}")
        logger.info(f"预计耗时: {script_info['estimated_time']}")

        # 修改脚本中的日期设置
        if start_date and end_date:
            self.modify_script_dates(script_file, start_date, end_date)

        start_time = time.time()

        try:
            # 构建命令
            cmd = [sys.executable, str(script_path)]

            # 运行脚本
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                cwd=str(self.current_dir)
            )
            
            # 实时输出日志
            def log_output(pipe, log_func):
                for line in iter(pipe.readline, ''):
                    if line.strip():
                        log_func(f"[{script_name}] {line.strip()}")
                pipe.close()
            
            # 创建线程来处理输出
            stdout_thread = threading.Thread(target=log_output, args=(process.stdout, logger.info))
            stderr_thread = threading.Thread(target=log_output, args=(process.stderr, logger.error))
            
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待进程完成
            return_code = process.wait()
            
            # 等待输出线程完成
            stdout_thread.join()
            stderr_thread.join()
            
            elapsed_time = time.time() - start_time
            
            if return_code == 0:
                logger.info(f"✓ {script_name} 执行成功，耗时: {elapsed_time:.1f}秒")
                success = True
            else:
                logger.error(f"✗ {script_name} 执行失败，返回码: {return_code}")
                success = False

        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"✗ {script_name} 执行异常: {e}，耗时: {elapsed_time:.1f}秒")
            success = False

        finally:
            # 恢复脚本原始版本
            if start_date and end_date:
                self.restore_script_backup(script_file)

        return success
    
    def run_all_scripts(self, skip_failed=True):
        """运行所有脚本"""
        logger.info("="*60)
        logger.info("开始执行所有数据获取脚本")
        logger.info("="*60)
        
        # 检查脚本文件
        if not self.check_scripts_exist():
            return False
        
        # 获取日期范围
        start_date, end_date = self.get_date_range()
        
        # 显示执行计划
        logger.info("\n执行计划:")
        for i, script in enumerate(self.scripts, 1):
            logger.info(f"{i}. {script['name']} - {script['description']} (预计{script['estimated_time']})")
        
        # 询问用户确认
        try:
            response = input("\n是否继续执行所有脚本? (y/n): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                logger.info("用户取消执行")
                return False
        except KeyboardInterrupt:
            logger.info("\n用户中断执行")
            return False
        
        # 开始执行
        total_start_time = time.time()
        success_count = 0
        failed_scripts = []
        
        for i, script in enumerate(self.scripts, 1):
            logger.info(f"\n{'='*40}")
            logger.info(f"执行进度: {i}/{len(self.scripts)}")
            logger.info(f"{'='*40}")
            
            success = self.run_script(script, start_date, end_date)
            
            if success:
                success_count += 1
            else:
                failed_scripts.append(script['name'])
                if not skip_failed:
                    logger.error("脚本执行失败，停止后续执行")
                    break
                else:
                    logger.warning("脚本执行失败，继续执行下一个脚本")
        
        # 执行总结
        total_elapsed = time.time() - total_start_time
        logger.info(f"\n{'='*60}")
        logger.info("执行总结")
        logger.info(f"{'='*60}")
        logger.info(f"总耗时: {total_elapsed:.1f}秒 ({total_elapsed/60:.1f}分钟)")
        logger.info(f"成功执行: {success_count}/{len(self.scripts)} 个脚本")
        
        if failed_scripts:
            logger.warning(f"失败的脚本: {', '.join(failed_scripts)}")
        else:
            logger.info("🎉 所有脚本执行成功!")
        
        return len(failed_scripts) == 0

def main():
    """主函数"""
    try:
        runner = ScriptRunner()
        success = runner.run_all_scripts(skip_failed=True)
        
        if success:
            logger.info("所有数据获取任务完成!")
        else:
            logger.warning("部分任务执行失败，请检查日志")
            
    except KeyboardInterrupt:
        logger.info("\n用户中断程序执行")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        raise

if __name__ == "__main__":
    main()
