#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全量数据启动脚本
专门用于获取全量历史数据，不设置日期限制
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from pathlib import Path

def run_script(script_name, description, estimated_time):
    """运行单个脚本"""
    script_path = Path(script_name)
    
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_name}")
        return False
    
    print(f"\n{'='*50}")
    print(f"🚀 开始执行: {script_name}")
    print(f"📝 描述: {description}")
    print(f"⏱️  预计耗时: {estimated_time}")
    print(f"📊 数据范围: 全量历史数据")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, str(script_path)],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {script_name} 执行成功")
            print(f"⏱️  实际耗时: {elapsed_time:.1f}秒 ({elapsed_time/60:.1f}分钟)")
            if result.stdout:
                # 显示最后几行输出
                stdout_lines = result.stdout.strip().split('\n')
                print("📋 执行结果:")
                for line in stdout_lines[-5:]:  # 显示最后5行
                    if line.strip():
                        print(f"   {line}")
            return True
        else:
            print(f"❌ {script_name} 执行失败")
            print(f"⏱️  耗时: {elapsed_time:.1f}秒")
            if result.stderr:
                print("❗ 错误信息:")
                stderr_lines = result.stderr.strip().split('\n')
                for line in stderr_lines[-10:]:  # 显示最后10行错误
                    if line.strip():
                        print(f"   {line}")
            return False
            
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ {script_name} 执行异常: {e}")
        print(f"⏱️  耗时: {elapsed_time:.1f}秒")
        return False

def main():
    """主函数"""
    print("🎯 全量数据获取启动脚本")
    print("="*60)
    print("📊 数据范围: 获取所有可用的历史数据")
    print("⚠️  注意: 全量数据获取需要较长时间，请耐心等待")
    print("="*60)
    
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 脚本列表 - 按执行顺序排列
    scripts = [
        {
            'name': '指数全量.py',
            'description': '获取所有指数的全量历史数据',
            'estimated_time': '5-10分钟'
        },
        {
            'name': 'tushare_index_data.py',
            'description': '获取指数成分股和变化率的全量数据',
            'estimated_time': '10-15分钟'
        },
        {
            'name': '全量股票日线.py',
            'description': '获取所有股票的全量日线数据和基本指标',
            'estimated_time': '3-6小时'
        },
        {
            'name': '财务数据分开表格全量更新.py',
            'description': '获取所有股票的全量财务报表数据',
            'estimated_time': '2-4小时'
        },
        {
            'name': '分红数据分开表格全量更新.py',
            'description': '获取所有股票的全量分红派息数据',
            'estimated_time': '30-60分钟'
        }
    ]
    
    # 显示执行计划
    print("\n📋 全量数据获取计划:")
    total_min_time = 0
    total_max_time = 0
    
    for i, script in enumerate(scripts, 1):
        print(f"{i}. {script['name']}")
        print(f"   📝 {script['description']}")
        print(f"   ⏱️  预计耗时: {script['estimated_time']}")
        
        # 估算总时间（粗略计算）
        time_str = script['estimated_time']
        if '小时' in time_str:
            if '-' in time_str:
                min_h, max_h = time_str.split('-')
                min_h = int(min_h)
                max_h = int(max_h.replace('小时', ''))
                total_min_time += min_h * 60
                total_max_time += max_h * 60
            else:
                h = int(time_str.replace('小时', ''))
                total_min_time += h * 60
                total_max_time += h * 60
        elif '分钟' in time_str:
            if '-' in time_str:
                min_m, max_m = time_str.split('-')
                min_m = int(min_m)
                max_m = int(max_m.replace('分钟', ''))
                total_min_time += min_m
                total_max_time += max_m
            else:
                m = int(time_str.replace('分钟', ''))
                total_min_time += m
                total_max_time += m
        print()
    
    print(f"📊 预计总耗时: {total_min_time//60}-{total_max_time//60} 小时")
    print(f"💾 建议: 执行前确保有足够的磁盘空间和稳定的网络连接")
    
    # 询问用户确认
    try:
        print("\n" + "="*60)
        response = input("❓ 确认开始全量数据获取? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("❌ 用户取消执行")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断执行")
        return
    
    # 开始执行
    total_start_time = time.time()
    success_count = 0
    failed_scripts = []
    
    for i, script in enumerate(scripts, 1):
        print(f"\n📊 执行进度: {i}/{len(scripts)}")
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        success = run_script(script['name'], script['description'], script['estimated_time'])
        
        if success:
            success_count += 1
        else:
            failed_scripts.append(script['name'])
            
            # 询问是否继续
            try:
                response = input(f"\n❓ {script['name']} 执行失败，是否继续执行下一个脚本? (y/n): ").strip().lower()
                if response not in ['y', 'yes', '是']:
                    print("❌ 用户选择停止执行")
                    break
            except KeyboardInterrupt:
                print("\n❌ 用户中断执行")
                break
    
    # 执行总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print("📊 全量数据获取总结")
    print(f"{'='*60}")
    print(f"🕐 开始时间: {datetime.fromtimestamp(total_start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🕐 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️  总耗时: {total_elapsed:.1f}秒 ({total_elapsed/60:.1f}分钟) ({total_elapsed/3600:.1f}小时)")
    print(f"✅ 成功执行: {success_count}/{len(scripts)} 个脚本")
    
    if failed_scripts:
        print(f"❌ 失败的脚本: {', '.join(failed_scripts)}")
        print("💡 建议: 检查失败脚本的错误信息，解决问题后重新运行")
    else:
        print("🎉 所有脚本执行成功!")
        print("📊 全量数据获取完成!")
    
    print(f"\n📋 数据存储位置:")
    print(f"   - 股票数据: qtdb 数据库")
    print(f"   - 指数数据: index 数据库") 
    print(f"   - 指数成分: ind 数据库")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        raise
